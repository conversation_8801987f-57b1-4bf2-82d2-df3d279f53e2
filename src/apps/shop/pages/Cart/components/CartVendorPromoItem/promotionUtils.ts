import { CartItemType } from '@/libs/cart/types';
import { PromoType } from '@/types/common';

function getMinimumQuantity(promo: PromoType) {
  return (
    promo.requirements.find(
      (requirement) => requirement.type === 'minimum_quantity',
    )?.minimumQuantity || 1
  );
}

function getFreeBenefit(promo: PromoType) {
  return promo.benefits?.find(
    (benefit) => benefit.type === 'give_free_product',
  );
}

export function getPromotionDetails({ item }: { item: CartItemType }) {
  const promotions: PromoType[] = item.product.promotions || [];
  const promo = promotions.find((p) => p.type === 'buy_x_get_y');
  if (!promo) return null;

  const minimumQuantity = getMinimumQuantity(promo);
  const freeBenefit = getFreeBenefit(promo);

  if (!freeBenefit) return null;

  const freeQtyPerTrigger = freeBenefit.quantity;
  const triggers = Math.floor(item.quantity / minimumQuantity);

  const freeItemsQty = triggers * freeQtyPerTrigger;

  const matchedOffer = item.product.offers.find(
    (offer) => offer.id === item.productOfferId,
  );
  const freeOffer = freeBenefit?.freeProductOffer ?? matchedOffer;

  return {
    promo,
    minimumQuantity,
    subtotalPaidItems: item.subtotal,
    subtotalAllItems:
      (item.quantity + freeItemsQty) * (Number(freeOffer?.price) * 1),
    freeItemsQty,
    freeOffer,
  };
}

export function groupItemsByPromotions(
  items: CartItemType[],
): Record<string, { items: CartItemType[] }> {
  return items.reduce(
    (acc, item) => {
      if (item.product.promotions) {
        item.product.promotions.forEach((promo) => {
          if (!acc[promo.type]) {
            acc[promo.type] = { items: [] };
          }
          acc[promo.type].items.push(item);
        });
      }
      return acc;
    },
    {} as Record<string, { items: CartItemType[] }>,
  );
}

export function getBuyXGetYPromotionDetails(items: CartItemType[]) {
  return items.reduce(
    (acc, item) => {
      const info = getPromotionDetails({ item });
      if (!info) return { ...acc };

      return {
        subtotalPaidItems: acc.subtotalPaidItems + Number(item.subtotal || 0),
        subtotalAllItems: acc.subtotalAllItems + info.subtotalAllItems,
        paidItemsQty: acc.paidItemsQty + item.quantity,
        freeItemsQty: acc.freeItemsQty + info.freeItemsQty,
        promotion: info.promo,
      };
    },
    {
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
    },
  );
}
