import { CartItemType } from '@/libs/cart/types';
import { PromoType } from '@/types/common';

function getMinimumQuantity(promo: PromoType) {
  return (
    promo.requirements.find(
      (requirement) => requirement.type === 'minimum_quantity',
    )?.minimumQuantity || 1
  );
}

function getFreeBenefit(promo: PromoType) {
  return promo.benefits?.find(
    (benefit) => benefit.type === 'give_free_product',
  );
}

export function getPromotionDetails({ item }: { item: CartItemType }) {
  const promotions: PromoType[] = item.product.promotions || [];
  const promo = promotions.find((p) => p.type === 'buy_x_get_y');
  if (!promo) return null;

  const minimumQuantity = getMinimumQuantity(promo);
  const freeBenefit = getFreeBenefit(promo);

  if (!freeBenefit) return null;

  const freeQtyPerTrigger = freeBenefit.quantity;
  const triggers = Math.floor(item.quantity / minimumQuantity);

  const freeItemsQty = triggers * freeQtyPerTrigger;

  const matchedOffer = item.product.offers.find(
    (offer) => offer.id === item.productOfferId,
  );
  const freeOffer = freeBenefit?.freeProductOffer ?? matchedOffer;

  return {
    minimumQuantity,
    subtotal: item.subtotal,
    subtotalNoDiscount: freeItemsQty * (Number(freeOffer?.price) * 1),
    paidItemsQty: item.quantity,
    freeItemsQty,
    freeOffer,
    freeBenefit,
  };
}

export function groupItemsByPromotions(
  items: CartItemType[],
): Record<string, { items: CartItemType[] }> {
  return items.reduce(
    (acc, item) => {
      if (item.product.promotions) {
        item.product.promotions.forEach((promo) => {
          if (!acc[promo.type]) {
            acc[promo.type] = { items: [] };
          }
          acc[promo.type].items.push(item);
        });
      }
      return acc;
    },
    {} as Record<string, { items: CartItemType[] }>,
  );
}

export function getBuyXGetYPromotionDetails(items: CartItemType[]) {
  const data = items.reduce(
    (acc, item) => {
      const { freeItems } = getPromotionDetails({ item });
      return {
        totalPaidItems: acc.totalPaidItems + item.quantity,
        totalFreeItems: acc.totalFreeItems + freeItems,
      };
    },
    { totalPaidItems, totalFreeItems },
  );
}
